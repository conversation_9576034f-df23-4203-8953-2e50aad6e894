<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e8c98287-96eb-49ea-baf6-ecdc63585ac9" name="更改" comment="完善">
      <change beforePath="$PROJECT_DIR$/脚本/character.lua" beforeDir="false" afterPath="$PROJECT_DIR$/脚本/character.lua" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/脚本/config/config.lua" beforeDir="false" afterPath="$PROJECT_DIR$/脚本/config/config.lua" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/脚本/五霸争雄.lua" beforeDir="false" afterPath="$PROJECT_DIR$/脚本/五霸争雄.lua" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="NewLua" />
      </list>
    </option>
  </component>
  <component name="GOROOT" url="file://C:\Users\<USER>\.nxproj\go" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GoLibraries">
    <option name="indexEntireGoPath" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="31AKW2ortf0GgufyOT8uvKBAhnJ" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.formatter.settings.were.checked&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.migrated.go.modules.settings&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;go.import.settings.migrated&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/脚本目录/五霸争雄/脚本&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\脚本目录\五霸争雄\脚本" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\脚本目录\五霸争雄\脚本" />
    </key>
  </component>
  <component name="RunManager">
    <configuration default="true" type="lua.app" factoryName="Lua Application">
      <option name="program" value="lua.exe" />
      <option name="file" value="" />
      <option name="workingDir" value="$PROJECT_DIR$/脚本" />
      <option name="debuggerType" value="1" />
      <option name="params" value="" />
      <option name="charset" value="UTF-8" />
      <option name="showConsole" value="true" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26094.121" />
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-IU-251.26094.121" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="e8c98287-96eb-49ea-baf6-ecdc63585ac9" name="更改" comment="" />
      <created>1754961470106</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754961470106</updated>
      <workItem from="1754961471265" duration="24048000" />
      <workItem from="1754992057496" duration="36391000" />
      <workItem from="1755078660217" duration="14363000" />
    </task>
    <task id="LOCAL-00001" summary="完善功能">
      <option name="closed" value="true" />
      <created>1754970100533</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1754970100533</updated>
    </task>
    <task id="LOCAL-00002" summary="完善界面功能">
      <option name="closed" value="true" />
      <created>1754988075056</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1754988075056</updated>
    </task>
    <task id="LOCAL-00003" summary="完善">
      <option name="closed" value="true" />
      <created>1754991822538</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1754991822538</updated>
    </task>
    <task id="LOCAL-00004" summary="完善">
      <option name="closed" value="true" />
      <created>1754998234802</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1754998234802</updated>
    </task>
    <task id="LOCAL-00005" summary="完善">
      <option name="closed" value="true" />
      <created>1754998806794</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1754998806794</updated>
    </task>
    <task id="LOCAL-00006" summary="完善">
      <option name="closed" value="true" />
      <created>1754999504566</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1754999504566</updated>
    </task>
    <task id="LOCAL-00007" summary="完善">
      <option name="closed" value="true" />
      <created>1754999745795</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1754999745795</updated>
    </task>
    <task id="LOCAL-00008" summary="完善">
      <option name="closed" value="true" />
      <created>1755003634027</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1755003634027</updated>
    </task>
    <task id="LOCAL-00009" summary="完善">
      <option name="closed" value="true" />
      <created>1755004648549</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1755004648549</updated>
    </task>
    <task id="LOCAL-00010" summary="完善">
      <option name="closed" value="true" />
      <created>1755053142469</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1755053142469</updated>
    </task>
    <task id="LOCAL-00011" summary="完善">
      <option name="closed" value="true" />
      <created>1755053149179</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1755053149179</updated>
    </task>
    <task id="LOCAL-00012" summary="完善">
      <option name="closed" value="true" />
      <created>1755060274255</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1755060274255</updated>
    </task>
    <task id="LOCAL-00013" summary="完善">
      <option name="closed" value="true" />
      <created>1755060675963</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1755060675963</updated>
    </task>
    <task id="LOCAL-00014" summary="完善">
      <option name="closed" value="true" />
      <created>1755063934642</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1755063934642</updated>
    </task>
    <task id="LOCAL-00015" summary="完善">
      <option name="closed" value="true" />
      <created>1755065563165</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1755065563165</updated>
    </task>
    <task id="LOCAL-00016" summary="完善">
      <option name="closed" value="true" />
      <created>1755065926251</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1755065926251</updated>
    </task>
    <task id="LOCAL-00017" summary="完善工具函数">
      <option name="closed" value="true" />
      <created>1755073741004</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1755073741004</updated>
    </task>
    <task id="LOCAL-00018" summary="完善">
      <option name="closed" value="true" />
      <created>1755093120419</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1755093120419</updated>
    </task>
    <task id="LOCAL-00019" summary="完善">
      <option name="closed" value="true" />
      <created>1755093661186</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1755093661186</updated>
    </task>
    <option name="localTasksCounter" value="20" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="完善功能" />
    <MESSAGE value="完善界面功能" />
    <MESSAGE value="完善工具函数" />
    <MESSAGE value="完善" />
    <option name="LAST_COMMIT_MESSAGE" value="完善" />
  </component>
  <component name="VgoProject">
    <integration-enabled>false</integration-enabled>
    <settings-migrated>true</settings-migrated>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>