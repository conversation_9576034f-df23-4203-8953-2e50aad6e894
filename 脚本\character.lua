local common = require("init.common")
local uiConfig = require("config.config").uiConfig
local config = require("config.config")
local utils = require("init.utils")

-- 辅助函数：安全输入文本并发送
-- 参数：content - 要输入的文本内容
-- 返回：boolean - 是否成功设置文本
local function safeInputText(content)
    if type(content) ~= "string" then
        content = tostring(content or "")
    end
    
    local 重试次数 = 0
    local 最大重试次数 = 3
    local 文本已设置 = false
    
    print("开始安全输入文本: " .. content)
    
    while 重试次数 < 最大重试次数 and not 文本已设置 do
        -- 获取输入框节点
        local sel = config.开发设置.输入框节点()
        local node = sel:findOne(1500)
        
        if node ~= nil then
            local currentText = node:text() or ""
            print("当前输入框文本: " .. currentText)
            
            -- 如果输入框已有内容，直接使用
            if currentText ~= "" and currentText == content then
                print("输入框已有正确文本，直接发送")
                文本已设置 = true
                break
            end
        end
        
        -- 执行setText
        print("执行setText，重试次数: " .. (重试次数 + 1))
        imeLib.setText(content)
        sleep(1200)
        
        -- 验证setText是否成功
        node = sel:findOne(1500)
        if node ~= nil then
            local newText = node:text() or ""
            print("setText后文本: " .. newText)
            
            if newText ~= "" and (newText == content or newText:find(content)) then
                print("setText成功")
                文本已设置 = true
            else
                重试次数 = 重试次数 + 1
                print("setText失败，准备重试")
                sleep(500)
            end
        else
            print("无法获取输入框节点进行验证")
            重试次数 = 重试次数 + 1
            sleep(500)
        end
    end
    
    if not 文本已设置 then
        print("setText重试失败，使用传统方式")
        imeLib.setText(content)
        sleep(1200)
    end
    
    -- 按下Enter发送
    keyDown("enter")
    sleep(1500)
    
    return 文本已设置
end


local Character = {}
Character.__index = Character

function Character:new()
    local instance = {}
    setmetatable(instance, Character)
    return instance
end

function Character:开启目标选择怪物界面()

    --防止开启了目标界面但是未选中
    common.findMultiColorAndExecute(uiConfig.目标_未选中怪物, "tap", 500)

    local 目标开启 = common.findMultiColor(uiConfig.目标_怪物)
    if not 目标开启 then
        tap(table.unpack(uiConfig.目标_变化))
        sleep(1000)
        return true
    else

        print("目标选择已开启")
    end
end

function Character:锁定懒人输入法()
    setStopCallBack(function(error)
        imeLib.unlock()
    end)

    imeLib.lock()
end

function Character:进入活动界面(界面)
    local result = common.findMultiColorAndExecute(uiConfig[界面], "tap", 1000)
    if result then
        return true
    end

    tap(table.unpack(uiConfig.活动页面_变化))
    sleep(1000)

    result = common.findMultiColorAndExecute(uiConfig[界面], "tap", 1000)
    if result then
        return true
    end

    return false
end

function Character:进入打宝地图(地图)
    -- 进入活动界面 -> 打宝
    local entered = self:进入活动界面("打宝")
    if not entered then
        print("进入活动界面失败: 打宝")
        return false
    end

    -- 读取地图动作列表
    local 地图动作列表 = uiConfig.打宝地图 and uiConfig.打宝地图[地图]
    if type(地图动作列表) ~= "table" or #地图动作列表 == 0 then
        print("未找到打宝地图配置: " .. tostring(地图))
        return false
    end

    -- 逐步解析并执行
    for idx, 动作 in ipairs(地图动作列表) do
        local 类型 = 动作.type
        local 参数 = 动作.参数
        -- 间隔时间优先取动作的间隔时间，否则从参数内的间隔时间字段兜底
        local 间隔 = 动作.间隔时间 or (type(参数) == "table" and 参数.间隔时间) or 0
        local 次数 = 动作.重复次数 or 1

        print(string.format("执行第 %d 步，类型=%s，重复次数=%d，间隔=%s", idx, tostring(类型), 次数, tostring(间隔)))

        if 类型 == "tap" then
            if type(参数) == "table" and #参数 >= 2 then
                tap(参数[1], 参数[2])
            else
                print(string.format("第 %d 步 tap 参数无效: %s", idx, tostring(参数)))
            end

        elseif 类型 == "swipe" then
            if type(参数) == "table" and #参数 >= 5 then
                for i = 1, 次数 do
                    swipe(参数[1], 参数[2], 参数[3], 参数[4], 参数[5])
                    -- 重复之间的等待
                    if i < 次数 and 间隔 and 间隔 > 0 then
                        sleep(间隔)
                    end
                end
            else
                print(string.format("第 %d 步 swipe 参数无效: %s", idx, tostring(参数)))
            end

        elseif 类型 == "ocr_tap" then
            -- ocr_tap 使用 common.findTextAndExecute 进行点击
            if type(参数) == "table" then
                local area = 参数.ocr_area or 参数.area or 参数.区域
                local text = 参数.search_text or 参数.text or 参数.文字
                if area and text then
                    -- 根据指示：ocr_tap 就是 common 的 findTextAndExecute
                    -- 这里不使用内部waitTime，统一用外部 sleep(间隔)
                    common.findTextAndExecute(text, area, "tap", true, 0)
                else
                    print(string.format("第 %d 步 ocr_tap 缺少 ocr_area 或 search_text", idx))
                end
            else
                print(string.format("第 %d 步 ocr_tap 参数无效: %s", idx, tostring(参数)))
            end

        else
            print(string.format("未知动作类型: %s (第 %d 步)", tostring(类型), idx))
        end

        -- 步骤结束后的统一间隔
        if 间隔 and 间隔 > 0 then
            sleep(间隔)
        end
    end

    return true
end

function Character:选择激战Boss目标(激战Boss目标)
    self:进入活动界面("激战Boss")

    tap(table.unpack(uiConfig["激战Boss子页面"][激战Boss目标]))

    sleep(1500)
end

function Character:执行喊话(喊话内容)
    -- 1) 检查进入游戏标识
    local inGame = common.findMultiColor(uiConfig.进入游戏标识)
    if not inGame then
        print("未检测到进入游戏标识，取消喊话")
        return false
    end

    -- 2) 打开喊话界面
    tap(table.unpack(uiConfig.喊话.打开喊话界面))
    sleep(1500)

    -- 3) 找色“世界”界面并点击
    common.findMultiColorAndExecute(uiConfig.喊话.世界界面, "tap", 800)

    -- 4) 点击喊话输入框
    tap(table.unpack(uiConfig.喊话.喊话输入框))
    sleep(1800)

    -- 5) 输入文本并发送
    safeInputText(喊话内容)

    -- 5.1) 找色“发送”并点击
    common.findMultiColorAndExecute(uiConfig.喊话.发送, "tap", 800)

    -- 6) 关闭喊话页面
    tap(table.unpack(uiConfig.喊话.关闭喊话页面))
    sleep(800)

    return true
end

function Character:攻击第一个目标怪物()
    tap(table.unpack(uiConfig.挂机设置["目标_第一个怪物"]))
    sleep(100)
end

function Character:领取散人福利()
    local entered = self:进入活动界面("散人福利")
    if not entered then
        print("进入活动界面失败: 散人福利")
        return false
    end
    sleep(3000)

    local 选项范围 = { uiConfig.散人福利_选项页面[1], uiConfig.散人福利_选项页面[2],
                       uiConfig.散人福利_选项页面[3], uiConfig.散人福利_选项页面[4], "红点.png", 0.85 }
    local 领取范围 = { uiConfig.散人福利_领取页面[1], uiConfig.散人福利_领取页面[2],
                       uiConfig.散人福利_领取页面[3], uiConfig.散人福利_领取页面[4], "红点.png", 0.85 }

    local function sortByY(列表)
        if not 列表 or #列表 == 0 then
            return 列表
        end
        table.sort(列表, function(a, b)
            local ay = a.y or a[2] or 0
            local by = b.y or b[2] or 0
            return ay < by
        end)
        return 列表
    end

    local function 领取循环()
        local guard = 0
        while guard < 10 do
            local 列表 = common.findPicAllPoint(领取范围)
            if not 列表 or #列表 == 0 then
                print("散人福利：领取页无红点，结束当前选项处理")
                break
            end

            列表 = sortByY(列表)

            for _, p in ipairs(列表) do
                local x = p.x or p[1]
                local y = p.y or p[2]
                if x and y then
                    tap(math.max(0, x - 50), math.max(0, y + 25))
                    sleep(1000)
                end
            end

            guard = guard + 1
            sleep(1000)
        end

        if guard >= 10 then
            print("散人福利：领取循环保护退出")
        end
    end

    local 选项列表 = common.findPicAllPoint(选项范围)
    if not 选项列表 or #选项列表 == 0 then
        print("散人福利：选项页未发现红点")
        return false
    end

    选项列表 = sortByY(选项列表)

    for _, pt in ipairs(选项列表) do
        local x = pt.x or pt[1]
        local y = pt.y or pt[2]
        if x and y then
            tap(math.max(0, x - 50), math.max(0, y + 25))
            sleep(2500)
            领取循环()
        end
    end

    return self:关闭活动界面()
end

function Character:关闭活动界面(关闭坐标)

    if 关闭坐标 then
        tap(table.unpack(关闭坐标))
        sleep(1000)
        return true
    else
        local result = common.findMultiColorAndExecute(uiConfig.关闭页面, "tap", 1000)
        return result
    end

end

function Character:入道天途()
    self:选择激战Boss目标("入道天途")

    sleep(2000)

    for i = 1, 3 do
        tap(table.unpack(uiConfig.入道天途_扫荡上层))
        sleep(500)
    end

    sleep(100)

    self:关闭活动界面()
end

function Character:禁地(层列表)
    -- 1) 进入“激战Boss -> 禁地”界面
    self:选择激战Boss目标("禁地")
    sleep(800)

    -- 2) 若未传入层列表，则读取用户配置中禁地.层数
    if 层列表 == nil then
        local userConf = require("config.config").userConfig
        层列表 = userConf and userConf.禁地 and userConf.禁地.层数 or {}
    end

    -- 3) 规范化层名称，兼容 "一层"/"二层" 及 "1层"/"2层" 等
    local 数字转中文 = { ["1"] = "一", ["2"] = "二", ["3"] = "三", ["4"] = "四", ["5"] = "五", ["6"] = "六", ["7"] = "七", ["8"] = "八", ["9"] = "九", ["10"] = "十" }
    local function 规范化层名(s)
        if not s then
            return nil
        end
        s = tostring(s)
        -- 已是“汉字层”
        if uiConfig.禁地[s] then
            return s
        end
        -- 数字层："2层" -> "二层"
        local num = s:match("(%d+)层")
        if num then
            local cn = (数字转中文[num] or num) .. "层"
            if uiConfig.禁地[cn] then
                return cn
            end
        end
        -- 仅数字："2" -> "二层"
        if 数字转中文[s] then
            local cn = 数字转中文[s] .. "层"
            if uiConfig.禁地[cn] then
                return cn
            end
        end
        -- 汉字无“层”："二" -> "二层"
        local cn2 = s .. "层"
        if uiConfig.禁地[cn2] then
            return cn2
        end
        return nil
    end

    local 目标层 = {}
    if type(层列表) == "table" then
        for _, v in ipairs(层列表) do
            local nm = 规范化层名(v)
            if nm then
                table.insert(目标层, nm)
            else
                print("禁地：无法识别层 -> " .. tostring(v))
            end
        end
    elseif type(层列表) == "string" then
        local nm = 规范化层名(层列表)
        if nm then
            table.insert(目标层, nm)
        end
    end

    if #目标层 == 0 then
        print("禁地：未提供有效层列表，已取消")
        return false
    end

    -- 4) 逐层执行：点击层 -> 连点“一键扫荡”15次（300ms间隔）
    for _, 层名 in ipairs(目标层) do
        local 坐标 = uiConfig.禁地[层名]
        if not 坐标 then
            print("禁地：未找到层配置 -> " .. tostring(层名))
        else
            print("禁地：开始扫荡 -> " .. tostring(层名))
            tap(table.unpack(坐标))
            sleep(1500)
            for _ = 1, 15 do
                tap(table.unpack(uiConfig.禁地.一键扫荡))
                sleep(300)
            end
            sleep(300)
        end
    end

    -- 5) 关闭活动界面
    self:关闭活动界面()
    return true
end

function Character:开启自动战斗功能()
    local 自动战斗 = common.findMultiColor(uiConfig.自动挂机中)
    if not 自动战斗 then
        tap(table.unpack(uiConfig.挂机按钮))
        sleep(500)
        return true
    else
        print("自动战斗已开启")
    end
end

function Character:世界首领()
    self:选择激战Boss目标("世界首领")
    sleep(2000)
    
    -- 获取剩余次数并解析
    print("获取剩余次数")
    local 获取剩余次数 = common.processOcr(570,547,802,655)
    local 剩余次数 = 0
    
    if 获取剩余次数 and type(获取剩余次数) == "table" then
        for _, item in ipairs(获取剩余次数) do
            if item.label and type(item.label) == "string" then
                local 数字 = item.label:match("^(%d+)$")
                if 数字 then
                    剩余次数 = tonumber(数字) or 0
                    print("找到剩余次数: " .. 剩余次数)
                    break
                end
            end
        end
    end
    
    if 剩余次数 == 0 then
        print("剩余次数为0或未找到，任务结束")
        return false
    end
    
    -- 检查Boss存活状态
    print("检查Boss存活状态")
    local Boss存活 = common.findTextAndGetCenter("尚存活", {808,509,1100,566})
    
    if not Boss存活 then
        print("Boss未存活，任务结束")
        return false
    end
    
    print("Boss存活，开始挑战")
    -- 前往挑战
    local 挑战成功 = common.findTextAndExecute("前往挑战", {806,472,1106,651}, "tap", true, 3000)
    if not 挑战成功 then
        print("前往挑战失败")
        return false
    end
    
    -- 进入战斗循环
    print("进入战斗循环，等待抢归属")
    local timer = utils.Timer:new()
    local 攻打完毕时间 = uiConfig.世界首领.攻打完毕时间
    
    while timer:elapsed("s") <= 攻打完毕时间 do
        -- 启用挂机功能
        self:开启自动战斗功能()
        
        -- 检查抢归属条件
        local 抢归属 = common.findTextAndGetCenter("抢归属", {41,161,277,276})
        
        if 抢归属 then
            print("检测到抢归属，重置计时器")
            timer:reset()
        end

        if common.findMultiColorAndExecute(uiConfig.世界首领.领取奖励,"tap","1000") then
            break
        end
        
        sleep(1000)
    end
    
    print("攻打完毕时间已到，执行回城")
    self:使用回城石()
    sleep(2000)
    
    return true
end

function Character:个人首领(分配次数)
    self:选择激战Boss目标("个人首领")
    sleep(2000)
    
    -- 如果没有传入参数，使用配置文件中的默认值
    if not 分配次数 then
        local userConf = require("config.config").userConfig
        分配次数 = userConf and userConf.个人首领 and userConf.个人首领.分配次数 or {}
    end
    
    -- 找到第一个不为0的项目
    local 首选项目 = nil
    for 项目, 次数 in pairs(分配次数) do
        if 次数 and 次数 > 0 and uiConfig.个人首领[项目] then
            首选项目 = 项目
            print("选择进入项目: " .. 项目)
            break
        end
    end
    
    if not 首选项目 then
        print("没有可用的个人首领项目，任务结束")
        return false
    end
    
    -- 点击首选项目进入
    tap(table.unpack(uiConfig.个人首领[首选项目]))
    print("点击" .. 首选项目 .. "，等待进入主地图")
    sleep(2000)
    
    -- 重新进入个人首领界面
    print("重新进入个人首领界面")
    self:选择激战Boss目标("个人首领")
    sleep(1500)
    
    -- 按照分配次数循环点击各个项目
    print("开始分配次数循环点击")
    for 项目, 次数 in pairs(分配次数) do
        if 次数 and 次数 > 0 and uiConfig.个人首领[项目] then
            print("点击" .. 项目 .. " " .. 次数 .. "次")
            for i = 1, 次数 do
                tap(table.unpack(uiConfig.个人首领[项目]))
                sleep(300)
            end
        end
    end
    
    print("分配完成，关闭活动界面")
    self:关闭活动界面()
    sleep(1000)
    
    -- 进入战斗循环
    print("进入战斗循环，监控打Boss标识")
    local timer = utils.Timer:new()
    local 完毕阈值 = uiConfig.个人首领.完毕阈值
    
    while timer:elapsed("s") <= 完毕阈值 do
        -- 启用挂机功能
        self:开启自动战斗功能()
        
        -- 检查打Boss标识
        local 打Boss标识 = common.findMultiColor(uiConfig.个人首领.打Boss标识)
        
        if 打Boss标识 then
            print("检测到打Boss标识，重置计时器")
            timer:reset()
        end
        
        sleep(1000)
    end
    
    print("完毕阈值时间已到，执行回城")
    self:使用回城石()
    sleep(2000)
    
    return true
end

function Character:领取宝箱()
    print("执行领取宝箱")
    for i, v in pairs(uiConfig.领取宝箱.tap集) do
        tap(table.unpack(v))
        sleep(1000)
    end

    for i = 1, uiConfig.领取宝箱.升级次数 do
        tap(uiConfig.领取宝箱.升级宝箱)
        sleep(100)
    end

    self:关闭活动界面(uiConfig.领取宝箱.背包关闭)

    print("宝箱领取完毕")
end

function Character:星空秘境()
    self:进入活动界面("活动")
    sleep(2000)

    -- 使用 M.findTextAndExecute 查找"星空秘境"文字并点击偏移位置
    local 成功 = common.findTextAndExecute(
        "星空秘境", 
        uiConfig.星空秘境.活动名字区域,
        function(centerPoint)
            -- 基于中心坐标加上前往偏移进行点击
            local 偏移x = uiConfig.星空秘境.前往偏移[1]
            local 偏移y = uiConfig.星空秘境.前往偏移[2]
            local 点击x = centerPoint.x + 偏移x
            local 点击y = centerPoint.y + 偏移y
            print(string.format("星空秘境: 中心点(%.2f,%.2f) + 偏移(%d,%d) = 点击位置(%d,%d)", 
                centerPoint.x, centerPoint.y, 偏移x, 偏移y, 点击x, 点击y))
            tap(点击x, 点击y)
        end,
        true, -- 完全匹配
        1000  -- 等待1秒
    )

    if not 成功 then
        print("星空秘境: 未找到星空秘境文字，使用备用方案")
        --暂时占位
        sleep(1000)
    end

    -- 前往地图
    tap(table.unpack(uiConfig.星空秘境.前往地图))
    sleep(6000)

    while true do
        -- 检查死亡状态循环
        while common.findMultiColor(uiConfig.星空秘境.已死亡) do
            print("检测到死亡状态，等待复活")
            sleep(1000)
        end

        -- 使用OCR监听"领取奖励"文字
        local 领取成功 = common.findTextAndExecute("领取奖励", uiConfig.星空秘境.领取奖励, "tap", false, 1000)
        
        if 领取成功 then
            print("检测到并点击了领取奖励")
            
            -- 进入确定循环，先点击退出打开确认界面，然后找到并点击"确定"
            while true do
                local 确定成功 = common.findTextAndExecute("确定", uiConfig.星空秘境.确定, "tap", false, 1000)
                
                if 确定成功 then
                    print("成功点击确定，星空秘境任务完成")
                    return true
                else
                    print("未找到确定按钮，点击退出坐标打开确认界面")
                    -- 点击退出坐标来打开确认界面
                    tap(table.unpack(uiConfig.星空秘境.退出))
                    sleep(1000)
                    -- 继续循环尝试找确定按钮
                end
            end
        else
            -- 如果没有找到领取奖励，继续执行原有的游戏逻辑
            -- 打开小地图
            tap(table.unpack(uiConfig.星空秘境.打开小地图))
            sleep(1000)

            -- 点击小地图躲藏位置
            tap(table.unpack(uiConfig.星空秘境.小地图躲藏位置))
            sleep(500)

            -- 关闭活动界面
            self:关闭活动界面()
            sleep(1000)

            -- 检测自动寻路中是否为真，为假三次后进入用户操作
            local 失败次数 = 0
            while 失败次数 < 3 do
                local 自动寻路中 = common.findMultiColor(uiConfig.星空秘境.自动寻路中)
                if 自动寻路中 then
                    break
                else
                    失败次数 = 失败次数 + 1
                    sleep(2000)
                end
            end

            if 失败次数 >= 3 then
                -- 进入用户自定义操作
                print("自动寻路连续失败3次，进入用户自定义操作")
                -- 这里用户可以自己实现具体操作
                sleep(5000)
            end

            sleep(2000)
        end
    end
end

function Character:弹窗关闭()
    if common.findTextAndGetCenter("点击前往",{898,305,1069,407}) then
        tap(table.unpack(uiConfig.弹窗处理.点击前往关闭))
    end
end

function Character:武林争霸()
    self:选择激战Boss目标("武林争霸")
    
    while true do
        print("寻找开战按钮")
        local 开战按钮存在 = common.findMultiColorAndExecute(uiConfig.武林争霸.开战, "tap", 1000)
        
        if not 开战按钮存在 then
            print("未找到开战按钮，任务结束")
            return true
        end
        if common.findMultiColorAndExecute(uiConfig.武林争霸.取消, "tap", 1000) then
            self:关闭活动界面(uiConfig.武林争霸.关闭按钮)
            return true
        end
        print("点击开战成功")
        local timer = utils.Timer:new()
        local 开战成功 = false
        
        while timer:elapsed("s") <= uiConfig.武林争霸.开战超时 do
            local result = self:使用回城石()
            
            if result then
                print("找到并点击回城石")
                sleep(1500)
                
                local 领取成功 = common.findTextAndExecute("领取", {642,539,747,593}, "tap", false, 1500)
                if 领取成功 then
                    print("成功领取奖励")
                end
                
                开战成功 = true
                break
            end
            
            sleep(1000)
        end
        
        if not 开战成功 then
            print("开战超时，继续下一轮")
        end
        
        print("等待1秒后继续开战")
        sleep(1000)
    end
end

function Character:使用回城石()
    local result = common.findMultiColorAndExecute(uiConfig.回城石, "tap", 1000)

    return result

end

function Character:加载游戏()
    print("开始加载游戏")
    local 总计时器 = utils.Timer:new()
    local 游戏加载超时 = uiConfig.加载游戏.游戏加载超时时间
    
    -- 检查并点击启动游戏_角色选择
    while 总计时器:elapsed("s") < 游戏加载超时 do
        -- 更新倒计时显示
        local remaining = 游戏加载超时 - 总计时器:elapsed("s")
        if _G.hudManager then
            _G.hudManager:update({text = string.format("游戏加载中，剩余:%ds", math.ceil(remaining))})
        end
        
        local 角色选择 = common.findMultiColorAndExecute(uiConfig.加载游戏.启动游戏_角色选择, "tap", 1000)
        if 角色选择 then
            print("点击角色选择")
            sleep(2000)
        end
        
        -- 检查并点击启动游戏_界面
        local 游戏界面 = common.findMultiColorAndExecute(uiConfig.加载游戏.启动游戏_界面, "tap", 1000)
        if 游戏界面 then
            print("点击启动游戏")
            sleep(2000)
        end
        
        -- 处理弹窗 - 点击弹窗自己的位置
        local 弹窗 = common.findMultiColorAndExecute(uiConfig.加载游戏.弹窗, "tap", 500)
        if 弹窗 then
            print("检测到弹窗并点击关闭")
        end
        
        -- 处理离线收益界面
        local 离线收益 = common.findMultiColor(uiConfig.加载游戏.离线收益界面)
        if 离线收益 then
            print("检测到离线收益界面，关闭")
            tap(table.unpack(uiConfig.加载游戏.离线收益关闭))
            sleep(500)
        end
        
        -- 处理公告界面
        local 公告 = common.findMultiColor(uiConfig.加载游戏.公告检测界面)
        if 公告 then
            print("检测到公告界面，关闭")
            tap(table.unpack(uiConfig.加载游戏.关闭公告))
            sleep(500)
        end
        
        -- 检查是否已进入游戏
        local 进入游戏 = common.findMultiColor(uiConfig.进入游戏标识)
        if 进入游戏 then
            print("检测到进入游戏标识，开始等待完全加载")
            
            -- 进入等待完全加载的循环
            local 加载计时器 = utils.Timer:new()
            local 等待完全加载时间 = uiConfig.加载游戏.等待完全加载时间
            
            while 加载计时器:elapsed("s") < 等待完全加载时间 do
                -- 更新等待完全加载的倒计时
                local waitRemaining = 等待完全加载时间 - 加载计时器:elapsed("s")
                if _G.hudManager then
                    _G.hudManager:update({text = string.format("等待完全加载，剩余:%ds", math.ceil(waitRemaining))})
                end
                
                -- 只检测离线收益关闭和弹窗关闭
                local 离线收益2 = common.findMultiColor(uiConfig.加载游戏.离线收益界面)
                if 离线收益2 then
                    print("加载中检测到离线收益，关闭")
                    tap(table.unpack(uiConfig.加载游戏.离线收益关闭))
                    sleep(500)
                end
                
                -- 弹窗点击自己位置关闭
                local 弹窗2 = common.findMultiColorAndExecute(uiConfig.加载游戏.弹窗, "tap", 500)
                if 弹窗2 then
                    print("加载中检测到弹窗并点击关闭")
                end
                
                sleep(1000)
            end
            
            print("游戏加载完成")
            return true
        end
        
        sleep(1000)
    end
    
    print("游戏加载超时")
    return false
end

function Character:千里传音(传音内容)
    print("开始千里传音")
    
    -- 1) 检查进入游戏标识
    local inGame = common.findMultiColor(uiConfig.进入游戏标识)
    if not inGame then
        print("未检测到进入游戏标识，取消千里传音")
        return false
    end

    -- 2) 执行tap传音
    tap(table.unpack(uiConfig.千里传音.传音))
    sleep(2000)

    -- 获取重复执行次数配置，默认为1
    local 重复次数 = uiConfig.千里传音.重复执行次数 or 1
    print("千里传音重复执行次数: " .. 重复次数)

    -- 3) 根据重复次数循环执行发送逻辑
    for i = 1, 重复次数 do
        print("执行第" .. i .. "次传音发送")
        
        -- 3.1) 点击输入框等1200ms
        tap(table.unpack(uiConfig.千里传音.输入框))
        sleep(1200)

        -- 3.2) 执行安全输入文本并发送
        safeInputText(传音内容)

        -- 3.3) tap本服发送
        tap(table.unpack(uiConfig.千里传音.本服发送))
        sleep(800)

        -- 如果不是最后一次，等待一段时间再继续
        if i < 重复次数 then
            print("等待进行下一次传音...")
            sleep(1000)
        end
    end

    -- 4) tap关闭传音
    tap(table.unpack(uiConfig.千里传音.关闭传音))
    sleep(800)

    print("千里传音完成，共执行" .. 重复次数 .. "次: " .. 传音内容)
    return true
end

return Character:new()