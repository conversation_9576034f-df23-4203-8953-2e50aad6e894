local coreLogic = {}

local character = require("character")
local config = require("config.config")
local utils = require("init.utils")
local common = require("init.common")

-- 状态管理
local state = {
    isHanging = false,  -- 是否在挂机
    isInMap = false,    -- 是否已进图
    isExecutingTask = false,  -- 是否正在执行定时任务
    currentTask = "",   -- 当前执行的任务名称
    worldBossFailCount = 0,  -- 世界首领失败次数
    lastWorldBossDate = "",  -- 上次世界首领日期
}

-- 定时器管理
local timers = {
    restart = nil,      -- 重启定时器
    shoutTimers = {},   -- 喊话定时器组
    forbiddenLand = nil,  -- 禁地定时器
    worldBoss = nil,    -- 世界首领定时器
    dailyTasks = {},    -- 每日任务定时器
    startupTasks = nil, -- 启动任务标记
}

-- 任务执行记录
local taskRecord = {
    startupTasksDone = false,  -- 启动任务是否已完成
    lastStartupDate = "",       -- 上次执行启动任务的日期
}

-- 获取当前时间字符串
local function getCurrentTime()
    return os.date("%Y-%m-%d %H:%M:%S")
end

-- 获取当前日期字符串
local function getCurrentDate()
    return os.date("%Y-%m-%d")
end

-- 获取当前小时和分钟
local function getCurrentHourMinute()
    local t = os.date("*t")
    return t.hour, t.min
end

-- 更新HUD显示
local function updateHUD(text)
    if _G.hudManager then
        _G.hudManager:update({text = text})
    end
    print("状态更新: " .. text)
end

-- 倒计时显示
local function showCountdown(action, seconds)
    for i = seconds, 1, -1 do
        updateHUD(string.format("%d秒后%s", i, action))
        utils.sleepLog(1000, "倒计时")
    end
end

-- 执行喊话逻辑
local function executeShout(shoutConfig, index)
    if not shoutConfig.enabled then
        return
    end
    
    showCountdown("执行喊话" .. index, 3)
    updateHUD("正在执行喊话" .. index)
    character:执行喊话(shoutConfig.content)
    print(string.format("喊话%d完成: %s", index, shoutConfig.content))
end

-- 执行攻击逻辑
local function executeAttack()
    character:开启目标选择怪物界面()
    utils.sleepLog(1000, "等待界面")
    character:攻击第一个目标怪物()
    print("攻击目标怪物完成")
end

-- 进入地图
local function enterMap()
    local mapConfig = config.userConfig
    if not mapConfig.map_enabled or not mapConfig.map_enabled.enable then
        updateHUD("地图功能未启用，跳过进图")
        return false
    end
    
    local mapName = mapConfig.map
    if not mapName or mapName == "" then
        updateHUD("未设置地图名称，跳过进图")
        return false
    end
    
    updateHUD("正在进入地图: " .. mapName)
    character:进入打宝地图(mapName)
    state.isInMap = true
    updateHUD("已进入地图: " .. mapName)
    return true
end

-- 初始化喊话定时器
local function initShoutTimers()
    local shoutSettings = config.userConfig.喊话设置
    if not shoutSettings then
        return
    end
    
    for i = 1, 10 do
        local key = "喊话" .. i
        local shoutConfig = shoutSettings[key]
        if shoutConfig and shoutConfig.enabled then
            timers.shoutTimers[i] = utils.Timer:new(shoutConfig.interval, "s")
            print(string.format("初始化喊话%d定时器，间隔: %d秒", i, shoutConfig.interval))
        end
    end
end

-- 检查并执行喊话
local function checkAndExecuteShouts()
    local shoutSettings = config.userConfig.喊话设置
    if not shoutSettings then
        return nil
    end
    
    local minTime = 999999
    local nextShout = nil
    
    for i = 1, 10 do
        local key = "喊话" .. i
        local shoutConfig = shoutSettings[key]
        local timer = timers.shoutTimers[i]
        
        if shoutConfig and shoutConfig.enabled and timer then
            if timer:isFinished() then
                executeShout(shoutConfig, i)
                timer:reset()
            else
                local remaining = timer:remaining("s")
                if remaining < minTime then
                    minTime = remaining
                    nextShout = i
                end
            end
        end
    end
    
    if nextShout then
        return string.format("喊话%d:%ds", nextShout, math.ceil(minTime))
    end
    return nil
end

-- 执行定时重启
local function executeRestart()
    updateHUD("正在执行定时重启...")
    print("执行定时重启")
    
    -- 关闭应用
    local packageName = config.开发设置.应用包名
    utils.close_app(packageName)
    updateHUD("已关闭游戏，等待6秒后重新启动")
    utils.sleepLog(6000, "等待游戏关闭")
    
    -- 启动应用
    updateHUD("正在启动游戏...")
    utils.start_app(packageName)
    utils.sleepLog(3000, "等待游戏启动")
    
    -- 重新加载游戏
    local result = character:加载游戏()
    
    if result then
        updateHUD("游戏加载完成")
    else
        updateHUD("游戏加载超时")
    end
    
    -- 重置状态
    state.isInMap = false
    updateHUD("重启完成，需要重新进图")
    
    -- 重新进图
    enterMap()
end

-- 检查定时重启
local function checkRestart()
    -- 仅在挂机状态下检查重启
    if not state.isHanging or state.isExecutingTask then
        return nil
    end
    
    local restartConfig = config.userConfig.定时重启
    if not restartConfig or not restartConfig.enable then
        return nil
    end
    
    if not timers.restart then
        timers.restart = utils.Timer:new(restartConfig.重启时间, "min")
        print(string.format("初始化重启定时器，间隔: %d分钟", restartConfig.重启时间))
    end
    
    if timers.restart:isFinished() then
        executeRestart()
        timers.restart:reset()
        return nil
    else
        local remaining = timers.restart:remaining("min")
        return string.format("重启:%dm", math.ceil(remaining))
    end
end

-- 执行禁地任务
local function executeJinDi()
    state.isExecutingTask = true
    state.currentTask = "禁地"
    updateHUD("正在执行禁地任务...")
    
    local jindiConfig = config.userConfig.禁地
    if jindiConfig and jindiConfig.enable then
        character:禁地(jindiConfig.层数)
        print("禁地任务完成")
    end
    
    state.isExecutingTask = false
    state.currentTask = ""
    state.isInMap = false  -- 执行任务后需要重新进图
end

-- 执行世界首领任务
local function executeWorldBoss()
    -- 检查日期，如果是新的一天，重置计数
    local currentDate = getCurrentDate()
    if state.lastWorldBossDate ~= currentDate then
        state.worldBossFailCount = 0
        state.lastWorldBossDate = currentDate
    end
    
    -- 如果今天已经失败两次，跳过
    if state.worldBossFailCount >= 2 then
        print("今日世界首领已失败2次，跳过执行")
        return
    end
    
    state.isExecutingTask = true
    state.currentTask = "世界首领"
    updateHUD("正在执行世界首领任务...")
    
    local result = character:世界首领()
    if result == false then
        state.worldBossFailCount = state.worldBossFailCount + 1
        print(string.format("世界首领失败，今日失败次数: %d", state.worldBossFailCount))
    else
        print("世界首领任务完成")
    end
    
    state.isExecutingTask = false
    state.currentTask = ""
    state.isInMap = false  -- 执行任务后需要重新进图
end

-- 执行每日任务
local function executeDailyTask(taskName)
    state.isExecutingTask = true
    state.currentTask = taskName
    updateHUD("正在执行" .. taskName .. "任务...")
    
    if taskName == "领取宝箱和散人福利" then
        if config.userConfig.升级宝箱 and config.userConfig.升级宝箱.enable then
            character:领取宝箱()
        end
        if config.userConfig.散人福利 and config.userConfig.散人福利.enable then
            character:领取散人福利()
        end
    elseif taskName == "星空秘境" then
        if config.userConfig.星空秘境 and config.userConfig.星空秘境.enable then
            character:星空秘境()
        end
    elseif taskName == "武林争霸" then
        if config.userConfig.武林争霸 and config.userConfig.武林争霸.enable then
            character:武林争霸()
        end
    end
    
    print(taskName .. "任务完成")
    state.isExecutingTask = false
    state.currentTask = ""
    state.isInMap = false  -- 执行任务后需要重新进图
end

-- 执行启动任务
local function executeStartupTasks()
    -- 检查是否是新的一天
    local currentDate = getCurrentDate()
    if taskRecord.lastStartupDate == currentDate then
        print("今日启动任务已执行，跳过")
        return
    end
    
    state.isExecutingTask = true
    
    -- 执行个人首领
    if config.userConfig.个人首领 and config.userConfig.个人首领.enable then
        state.currentTask = "个人首领"
        updateHUD("正在执行个人首领任务...")
        character:个人首领(config.userConfig.个人首领.分配次数)
        print("个人首领任务完成")
    end
    
    -- 执行入道天途
    if config.userConfig.入道天途 and config.userConfig.入道天途.enable then
        state.currentTask = "入道天途"
        updateHUD("正在执行入道天途任务...")
        character:入道天途()
        print("入道天途任务完成")
    end
    
    taskRecord.startupTasksDone = true
    taskRecord.lastStartupDate = currentDate
    
    state.isExecutingTask = false
    state.currentTask = ""
    state.isInMap = false  -- 执行任务后需要重新进图
end

-- 检查周期性任务
local function checkPeriodicTasks()
    -- 检查禁地任务（每6小时）
    if config.userConfig.禁地 and config.userConfig.禁地.enable then
        if not timers.forbiddenLand then
            -- 启动时立即执行一次禁地
            print("启动时执行禁地任务")
            executeJinDi()
            
            -- 初始化定时器，6小时后再次执行
            timers.forbiddenLand = utils.Timer:new(6, "h")
            print("初始化禁地定时器，间隔: 6小时")
        elseif timers.forbiddenLand:isFinished() then
            executeJinDi()
            timers.forbiddenLand:reset()
            
            -- 执行任务后重新进图
            if state.isHanging and not state.isInMap then
                enterMap()
            end
        end
    end
    
    -- 检查世界首领任务（每15分钟）
    if config.userConfig.世界Boss and config.userConfig.世界Boss.enable then
        if not timers.worldBoss then
            timers.worldBoss = utils.Timer:new(15, "min")
            print("初始化世界首领定时器，间隔: 15分钟")
        end
        
        if timers.worldBoss:isFinished() then
            executeWorldBoss()
            timers.worldBoss:reset()
            
            -- 执行任务后重新进图
            if state.isHanging and not state.isInMap then
                enterMap()
            end
        end
    end
end

-- 检查每日定时任务
local function checkDailyTasks()
    local hour, minute = getCurrentHourMinute()
    local currentTime = hour * 60 + minute  -- 转换为分钟
    
    -- 定义每日任务
    local dailyTasks = {
        {time = 22 * 60, name = "领取宝箱和散人福利", executed = false},
        {time = 20 * 60, name = "星空秘境", executed = false},
        {time = 13 * 60, name = "武林争霸", executed = false},
        {time = 21 * 60, name = "武林争霸", executed = false},
    }
    
    -- 检查并执行到时间的任务
    for _, task in ipairs(dailyTasks) do
        local taskKey = task.name .. "_" .. task.time
        if not timers.dailyTasks[taskKey] then
            timers.dailyTasks[taskKey] = {executed = false, lastDate = ""}
        end
        
        local currentDate = getCurrentDate()
        local taskTimer = timers.dailyTasks[taskKey]
        
        -- 如果是新的一天，重置执行状态
        if taskTimer.lastDate ~= currentDate then
            taskTimer.executed = false
            taskTimer.lastDate = currentDate
        end
        
        -- 检查是否到执行时间
        if currentTime >= task.time and currentTime < task.time + 5 and not taskTimer.executed then
            executeDailyTask(task.name)
            taskTimer.executed = true
            
            -- 执行任务后重新进图
            if state.isHanging and not state.isInMap then
                enterMap()
            end
        end
    end
end

-- 主挂机循环
function coreLogic.startHanging()
    print("开始主线挂机逻辑")
    updateHUD("正在初始化挂机系统...")
    
    -- 初始化
    state.isHanging = true
    initShoutTimers()
    
    -- 执行启动任务
    if not taskRecord.startupTasksDone then
        executeStartupTasks()
    end
    
    -- 首次检查周期性任务（会立即执行禁地）
    checkPeriodicTasks()
    
    -- 进入地图
    if enterMap() then
        -- 开启自动战斗
        utils.sleepLog(2000, "等待进图完成")
        character:开启自动战斗功能()
        updateHUD("已开启自动战斗，开始挂机")
    end
    
    -- 主循环
    while state.isHanging do
        -- 如果不在执行任务且需要进图
        if not state.isExecutingTask and not state.isInMap then
            enterMap()
            if state.isInMap then
                utils.sleepLog(2000, "等待进图完成")
                character:开启自动战斗功能()
            end
        end
        
        -- 挂机时的定期操作
        if state.isInMap and not state.isExecutingTask then
            -- 定期攻击（每3秒）
            if not timers.attackTimer then
                timers.attackTimer = utils.Timer:new(3, "s")
            end
            
            local statusParts = {"挂机中"}
            
            -- 添加攻击倒计时
            local attackRemaining = timers.attackTimer:remaining("s")
            table.insert(statusParts, string.format("攻击:%ds", math.ceil(attackRemaining)))
            
            if timers.attackTimer:isFinished() then
                executeAttack()
                timers.attackTimer:reset()
            end
            
            -- 检查并执行喊话，获取喊话状态
            local shoutStatus = checkAndExecuteShouts()
            if shoutStatus then
                table.insert(statusParts, shoutStatus)
            end
            
            -- 检查定时重启，获取重启状态
            local restartStatus = checkRestart()
            if restartStatus then
                table.insert(statusParts, restartStatus)
            end
            
            -- 组合显示状态
            updateHUD(table.concat(statusParts, " | "))
        end
        
        -- 检查定时任务（不受挂机状态限制）
        checkPeriodicTasks()
        checkDailyTasks()
        
        -- 短暂休眠，避免过度占用资源
        utils.sleepLog(1000, "主循环")
    end
end

-- 停止挂机
function coreLogic.stopHanging()
    print("停止挂机逻辑")
    state.isHanging = false
    updateHUD("挂机已停止")
end

-- 获取当前状态
function coreLogic.getState()
    return state
end

-- 重置状态
function coreLogic.resetState()
    state.isInMap = false
    state.isExecutingTask = false
    state.currentTask = ""
    updateHUD("状态已重置")
end

return coreLogic