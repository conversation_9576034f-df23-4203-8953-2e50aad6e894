local config = {}

--由用户进行配置的内容
config.userConfig = {


    定时重启 = {
        enable = true,
        重启时间 = 30  --单位是分钟

    },

    --地图配置
    map_enabled = { enable = false }, -- 是否启用进入地图功能
    map = "时空幽谷", --可选"离火圣殿二层"/"时空幽谷"/"无尽幽白一层"/"遗忘之地"

    喊话设置 = {
        -- 喊话1配置
        喊话1 = {
            enabled = true, -- 是否启用
            content = "收购各种材料，价格优惠！", -- 喊话内容
            interval = 15             -- 间隔秒数
        },

        -- 喊话2配置
        喊话2 = {
            enabled = true, -- 是否启用
            content = "出售高级装备，物美价廉！", -- 喊话内容
            interval = 20             -- 间隔秒数
        },
    },

    武林争霸 = { enable = false },

    世界Boss = { enable = false },

    禁地 = {
        enable = true,
        层数 = { "1层", "2层", "3层", "4层", "5层" }   --最高选择第五层
    },


    个人首领 = {
        enable = true,
        分配次数 = { 元素 = 2, 装备 = 3, 龙脉 = 5, 神器 = 5 }
    },

    入道天途 = { enable = true },

    升级宝箱 = { enable = true },

    散人福利 = { enable = true },

    星空秘境 = { enable = true },

    千里传音 = {
        enable = true,
        传音内容 = "",
    }


}

config.uiConfig = {
    打宝 = { 935, 14, 1014, 95, "f69447-101010", "-1|2|ec5139-101010|0|5|f69447-101010|1|10|f69447-101010|1|16|f28f1d-101010|-8|16|bc4221-101010|-11|20|a13318-101010|-13|25|f2e7b6-101010|-16|27|e7d690-101010|-13|31|b68730-101010", 0, 0.8 },

    激战Boss = { 848, 8, 938, 96, "46280e-101010", "3|5|f26a1b-101010|5|7|d6651c-101010|6|11|f16a1b-101010|8|12|f28e1d-101010|8|11|f7d01e-101010|8|8|f7d21e-101010|9|7|f7d31f-101010|11|9|fcf23e-101010", 0, 0.8 },

    散人福利 = { 939, 6, 1024, 92, "f36b1b-101010", "1|1|f36b1b-101010|1|3|de4318-101010|1|4|f6b873-101010|1|6|fbf8e3-101010|1|9|fbf8e3-101010|1|12|f2e7b6-101010|1|16|e0d7a3-101010|1|17|c8bf94-101010|1|19|c7b378-101010|1|22|a13318-101010|2|23|da9b4e-101010|1|25|b68730-101010|1|27|6f4017-101010", 0, 0.8 },

    活动 = { 765, 0, 848, 93, "ec5139-202020", "2|-2|bc4221-202020|6|-5|a13318-202020|8|-7|ec5139-202020|11|-7|de4318-202020|10|-2|ec5139-202020|11|2|341f0f-202020|11|8|fdf8ae-202020|11|14|f1744c-202020|12|18|fcf9c9-202020|12|21|9f824e-202020|11|27|8d3f2a-202020", 0, 0.8 },

    活动页面_变化 = { 1034, 29 },

    目标_怪物 = { 753, 389, 802, 427, "efd486-101010", "0|1|ecc771-101010|-1|3|ecc771-101010|0|5|e8b663-101010|0|6|e8b663-101010|1|8|dbab52-101010|4|8|dbab52-101010|6|8|dbab52-101010|9|8|dbab52-101010|9|9|dbab52-101010|10|11|d49b3d-101010|10|12|d49b3d-101010|13|9|dbab52-101010|17|8|e8b663-101010|20|8|dbab52-101010|21|8|dbab52-101010|22|6|e8b663-101010|21|2|ecc771-101010", 0, 0.8 },

    目标_变化 = { 1083, 305 },

    目标_未选中怪物 = { 757, 390, 798, 427, "ddd09b-202020", "-1|1|dcd09b-202020|-3|3|d6c686-202020|-2|7|cbb77b-202020|-1|7|cbb77b-202020|1|8|b9a771-202020|4|7|cbb77b-202020|6|6|7f6e40-202020|12|7|cbb77b-202020|15|8|a28d58-202020|18|8|b9a771-202020|19|6|cbb77b-202020|19|3|d6c686-202020|17|1|b9a771-202020", 0, 0.8 },

    自动挂机中 = { 672, 446, 707, 478, "f0f4f4-202020", "1|2|d6e8f2-202020|0|4|ade3ee-202020|0|6|bdddf0-202020|1|6|b3deee-202020|1|8|9fdee9-202020|1|10|8ae7ea-202020|2|10|90e6eb-202020|5|9|89e7ea-202020|7|7|b6deef-202020|-7|9|89e7ea-202020", 0, 0.8 },

    挂机按钮 = { 1169, 297 },

    激战Boss子页面 = {
        个人首领 = { 281, 415 },

        世界首领 = { 443, 250 },

        禁地 = { 564, 492 },

        入道天途 = { 723, 276 },

        武林争霸 = { 840, 475 }
    },

    打宝地图 = {
        无尽幽白一层 = {
            {
                type = "swipe",
                参数 = { 235, 246, 254, 585, 500 },
                重复次数 = 2,
                间隔时间 = 1000
            },
            {
                type = "tap",
                参数 = { 252, 237 },
                间隔时间 = 2000
            },
            {
                type = "tap",
                参数 = { 1025, 590 },
                间隔时间 = 1500
            }
        },
        时空幽谷 = {
            {
                type = "ocr_tap",
                参数 = {
                    ocr_area = { 173, 136, 357, 655 },
                    search_text = "第12大陆",
                    间隔时间 = 1500
                }
            },
            {
                type = "tap",
                参数 = { 1024, 410 },
                间隔时间 = 1500
            }
        },
        离火圣殿二层 = {
            {
                type = "swipe",
                参数 = { 235, 246, 254, 585, 500 },
                重复次数 = 2,
                间隔时间 = 1000
            },
            {
                type = "tap",
                参数 = { 252, 237 },
                间隔时间 = 2000
            },
            {
                type = "swipe",
                参数 = { 738, 618, 719, 346, 800 },
                重复次数 = 1,
                间隔时间 = 1000
            },
            {
                type = "tap",
                参数 = { 1032, 614 },
                间隔时间 = 1500
            }
        },
        遗忘之地 = {
            {
                type = "swipe",
                参数 = { 235, 246, 254, 585, 500 },
                重复次数 = 2,
                间隔时间 = 1000
            },
            {
                type = "tap",
                参数 = { 249, 446 },
                间隔时间 = 2000
            },
            {
                type = "tap",
                参数 = { 1023, 417 },
                间隔时间 = 1500
            }
        }
    },

    进入游戏标识 = { 405, 651, 445, 686, "181208-101010", "1|1|330f07-101010|2|2|5b100b-101010|3|3|72180d-101010|3|4|bb3510-101010|3|4|bb3510-101010|3|5|bb3510-101010|3|6|bb3510-101010|3|8|bb3510-101010|1|8|65240c-101010|1|9|95281d-101010|0|11|5b100b-101010|7|9|72180d-101010|7|11|bfb9a9-101010|7|12|fbf8d1-101010", 0, 0.8 },

    回城石 = { 457, 545, 819, 610, "11283c-202020", "-2|2|6887ac-202020|-4|4|204e7f-202020|-5|6|30619a-202020|-4|8|375e89-202020|0|8|386ea7-202020|4|8|164f84-202020|6|10|1760a3-202020|8|14|577290-202020", 0, 0.8 },

    喊话 = {
        打开喊话界面 = { 615, 668 },

        世界界面 = { 17, 310, 60, 375, "f6f6e9-101010", "0|1|f3f3e6-101010|0|3|f2f2e5-101010|0|4|f2f2e5-101010|-1|3|98968f-101010|-1|3|98968f-101010|-1|4|98968f-101010|-2|4|2e2b23-101010|-2|4|2e2b23-101010|-3|5|332f21-101010", 0, 0.8 },

        关闭喊话页面 = { 451, 97 },

        发送 = { 355, 330, 421, 366, "725327-101010", "1|6|725428-101010|3|9|684e29-101010|5|7|5e4e36-101010|7|7|eeede1-101010|13|6|87857f-101010|14|9|928d82-101010|20|10|533f22-101010|21|10|8a8882-101010|20|13|c8c7bd-101010|-15|-11|3a3423-101010", 0, 0.9 },

        喊话输入框 = { 157, 684 },


    },

    挂机设置 = {
        目标_第一个怪物 = { 836, 316 },
    },

    背包 = { 1247, 223 },

    散人福利_选项页面 = { 156, 128, 343, 646 },

    散人福利_领取页面 = { 346, 132, 1113, 661 },

    关闭页面 = { 1068, 75, 1123, 134, "5b6150-202020", "1|1|6a725d-202020|1|3|a8aea4-202020|1|4|969e8f-202020|1|6|27221b-202020|2|7|7c1c0d-202020|2|8|9b1e0a-202020|3|10|9b1e0a-202020|3|13|c1430e-202020|3|15|9c1f0b-202020|5|16|9d200c-202020|6|17|9d200c-202020|8|19|7d1d0e-202020|4|7|9b1e0a-202020|5|6|9b1e0a-202020|7|4|7d1d0d-202020", 0, 0.8 },

    入道天途_扫荡上层 = { 448, 610 },

    禁地 = {
        一层 = { 262, 186 },
        二层 = { 461, 165 },
        三层 = { 647, 178 },
        四层 = { 872, 178 },
        五层 = { 1079, 178 },

        一键扫荡 = { 968, 605 },

    },

    世界首领 = {
        攻打完毕时间 = 15,
        领取奖励 = { 586, 551, 650, 605, "d2d1d1-202020", "0|1|dcdcdc-202020|0|2|cccccc-202020|-1|2|d2d1d1-202020|-2|3|d2d1d1-202020|-3|4|d1d1d1-202020|-3|5|cac9c9-202020|-4|5|cbcaca-202020|-4|6|d3d3d3-202020|-5|6|c2c1c1-202020|-5|7|bfbebe-202020|0|2|cccccc-202020|1|2|cac9c9-202020|1|3|bab9b9-202020|2|3|cbcaca-202020|2|4|c0bfbf-202020|3|4|c9c8c8-202020|3|5|cbcaca-202020|4|5|bfbebe-202020|4|6|b9b8b8-202020|-2|-2|99622e-202020|-2|-1|754d2c-202020|-3|0|905a2c-202020|-4|1|a86431-202020|-5|3|93522b-202020|-5|3|93522b-202020", 0, 0.85 },
    },

    个人首领 = {
        元素 = { 279, 614 },
        装备 = { 519, 602 },
        龙脉 = { 753, 609 },
        神器 = { 981, 606 },

        打Boss标识 = { 734, 2, 773, 39, "e9ba66-202020", "1|2|e1b05a-202020|4|4|d9a74d-202020|5|4|be964a-202020|7|6|c29943-202020|9|9|b48435-202020|11|11|a5792f-202020|13|13|9a6e2c-202020|13|0|e9ba66-202020|8|4|d9a74d-202020|6|7|b69042-202020|2|12|9f7226-202020|-1|14|986923-202020", 0, 0.8 },

        完毕阈值 = 20
    },

    武林争霸 = {
        开战 = { 959, 493, 1052, 583, "fbf3af-202020", "3|1|fef9a4-202020|5|1|fdf9ab-202020|6|5|edd97b-202020|10|9|fcec74-202020|10|13|fce172-202020|10|18|ecc660-202020|10|23|f1a850-202020|10|27|f1954a-202020|10|32|e77e40-202020|18|35|fdf176-202020|20|39|f2d16c-202020|20|44|f1ce5b-202020|20|47|bc6a2d-202020", 0, 0.8 },
        开战超时 = 30,
        关闭按钮 = { 1128, 103 },
        取消 = { 495, 431, 555, 461, "fff4bb-202020", "1|2|927d51-202020|1|7|927d51-202020|0|9|fff4bb-202020|0|12|fff4bb-202020|0|13|fff4bb-202020|6|1|fff4bb-202020|7|1|fff4bb-202020|10|1|fff4bb-202020|12|2|f2e5ae-202020|11|5|fff4bb-202020|10|9|fff4bb-202020|9|12|f3e6af-202020|20|13|fff4bb-202020|20|16|634a23-202020", 0, 0.8 },

    },

    领取宝箱 = {
        tap集 = {
            { 1246, 223 },
            { 1089, 663 },
            { 1203, 306 },
            { 932, 301 },
            { 635, 438 }
        },
        升级宝箱 = { 1033, 639 },
        背包关闭 = { 571, 98 },
        升级次数 = 40,
    },


    弹窗处理 = {
        点击前往关闭 = { 1046, 195 }
    },

    星空秘境 = {
        活动名字区域 = { 328, 133, 595, 640 },
        前往偏移 = { 200, 10 },
        前往地图 = { 756, 592 },
        已死亡 = { 599, 142, 802, 246, "fc5857-202020", "0|7|e73e41-202020|-1|10|e73334-202020|-1|15|d42122-202020|5|15|e73334-202020|4|22|cf1b1c-202020|17|19|dd3032-202020|18|23|c91616-202020|26|18|dd3032-202020|30|15|e73334-202020|57|19|d42122-202020|-11|-39|1e1811-202020|75|25|cf1b1c-202020|75|39|c21112-202020", 0, 0.8 },
        领取奖励 = { 481, 519, 829, 633 },
        确定 = { 644,374,861,503 },
        退出 = { 322, 207 }
    },

    加载游戏 = {
        离线收益界面 = { 1028, 14, 1049, 44, "e0d6a4-101010", "1|1|f2e7ae-101010|2|2|f2e7ae-101010|4|3|f2e7ae-101010|5|5|fdfadc-101010|5|5|fdfadc-101010|3|7|776847-101010|2|8|8c7849-101010|0|11|aa935f-101010|2|16|dac787-101010|4|15|b79a5e-101010|6|12|b9a26a-101010|11|9|aa935f-101010|13|6|e0d5a4-101010|14|5|f2efd3-101010|11|3|eadecb-101010|9|1|ede1ab-101010|5|-3|e0d6a4-101010|1|-6|c5b075-101010", 0, 0.8 },

        启动游戏_界面 = { 541, 496, 733, 606, "c7ba6b-202020", "5|-1|beaf61-202020|7|-1|b6a65f-202020|7|4|e1d991-202020|8|8|f0ebb1-202020|8|12|fefcbc-202020|8|16|ede8ab-202020|8|20|cac78a-202020|-1|13|ede8ab-202020|-1|17|e5e0a5-202020|42|10|f2edba-202020|46|9|f8f5bd-202020|49|9|f2edba-202020|47|15|e2dea5-202020|49|19|c1b979-202020|44|20|615739-202020|40|16|d5d39e-202020", 0, 0.9 },

        启动游戏_角色选择 = { 589, 62, 758, 134, "f1e1ac-101010", "2|0|f5e8b5-101010|3|2|665043-101010|2|6|e1ba7a-101010|2|8|e7c88c-101010|3|9|e7c88c-101010|2|13|d7c597-101010|2|15|bfa87d-101010|26|10|e1ba7a-101010|26|12|d3b078-101010|32|16|e6c68a-101010|35|15|e2bc7d-101010|38|15|e5c487-101010|39|19|f9f2c1-101010|-41|-27|140a00-101010", 0, 0.9 },

        公告检测界面 = { 661, 78, 739, 121, "e2bb68-101010", "4|0|e2bb68-101010|7|-1|2f2814-101010|12|0|e2bb68-101010|9|5|e5cd83-101010|9|10|f8eba5-101010|9|13|fcf5b3-101010|1|13|eee7a8-101010|-7|13|fcf5b3-101010|0|16|62400b-101010|14|13|6d440c-101010|14|7|57380b-101010|17|2|4a320f-101010", 0, 0.9 },

        弹窗 = { 1028, 14, 1049, 44, "e0d6a4-101010", "1|1|f2e7ae-101010|2|2|f2e7ae-101010|4|3|f2e7ae-101010|5|5|fdfadc-101010|5|5|fdfadc-101010|3|7|776847-101010|2|8|8c7849-101010|0|11|aa935f-101010|2|16|dac787-101010|4|15|b79a5e-101010|6|12|b9a26a-101010|11|9|aa935f-101010|13|6|e0d5a4-101010|14|5|f2efd3-101010|11|3|eadecb-101010|9|1|ede1ab-101010|5|-3|e0d6a4-101010|1|-6|c5b075-101010", 0, 0.9 },

        关闭公告 = { 1093, 95 },

        离线收益关闭 = { 628, 539 },

        等待完全加载时间 = 20, --秒

        游戏加载超时时间 = 200, --秒
    },

    千里传音 = {
        传音 = { 943, 666 },
        本服发送 = { 884, 648 },
        输入框 = { 505, 642 },
        关闭传音 = { 1112, 103 },
        重复执行次数 = 2

    }

}

config.开发设置 = {
    应用包名 = "com.wbqxys.sfzy",
    输入框节点 = function()
        return packageName("com.wbqxys.sfzy"):checkable(false):checked(false):clickable(true):enabled(true):focusable(true):focused(true):visibleToUser(true)
    end

}
-- 从UI配置解析到用户配置
-- @param uiConfig: 来自init/ui.lua的UI界面配置数据
-- 主要功能：将UI控件数据解析转换为用户业务配置，更新config.userConfig
function config.updateFromUIConfig(uiConfig)
    if not uiConfig or type(uiConfig) ~= "table" then
        print("警告：uiConfig 为空或不是表格类型")
        return
    end

    print("开始更新用户配置...")

    -- 辅助函数：安全转换为布尔值
    local function toBool(value)
        if type(value) == "string" then
            return value == "true"
        elseif type(value) == "boolean" then
            return value
        else
            return false
        end
    end

    -- 辅助函数：安全转换为数字
    local function toNumber(value, default)
        local num = tonumber(value)
        return num or (default or 0)
    end

    -- 辅助函数：安全获取字符串值
    local function toString(value, default)
        if type(value) == "string" then
            return value
        else
            return default or ""
        end
    end

    -- 1. 更新定时重启配置
    if uiConfig["定时重启"] ~= nil then
        config.userConfig.定时重启.enable = toBool(uiConfig["定时重启"])
        print("更新定时重启: " .. tostring(config.userConfig.定时重启.enable))
    end

    if uiConfig["定时重启时间"] ~= nil then
        config.userConfig.定时重启.重启时间 = toNumber(uiConfig["定时重启时间"], 30)
        print("更新定时重启时间: " .. tostring(config.userConfig.定时重启.重启时间))
    end

    -- 2. 更新地图配置
    if uiConfig["进入地图"] ~= nil then
        config.userConfig.map_enabled.enable = toBool(uiConfig["进入地图"])
        print("更新进入地图: " .. tostring(config.userConfig.map_enabled.enable))
    end

    -- 地图选择（下图模式对应spinner的索引）
    if uiConfig["下图模式"] ~= nil then
        local mapIndex = toNumber(uiConfig["下图模式"], 0)
        local mapList = { "无尽幽白一层", "离火圣殿二层", "时空幽谷", "遗忘之地" }
        if mapIndex >= 0 and mapIndex < #mapList then
            config.userConfig.map = mapList[mapIndex + 1] -- Lua数组从1开始
            print("更新地图选择: " .. tostring(config.userConfig.map))
        end
    end

    -- 3. 更新喊话设置
    if uiConfig["喊话1"] ~= nil then
        config.userConfig.喊话设置.喊话1.enabled = toBool(uiConfig["喊话1"])
        print("更新喊话1启用: " .. tostring(config.userConfig.喊话设置.喊话1.enabled))
    end

    if uiConfig["喊话1内容"] ~= nil then
        config.userConfig.喊话设置.喊话1.content = toString(uiConfig["喊话1内容"], "")
        print("更新喊话1内容: " .. tostring(config.userConfig.喊话设置.喊话1.content))
    end

    if uiConfig["喊话1间隔"] ~= nil then
        config.userConfig.喊话设置.喊话1.interval = toNumber(uiConfig["喊话1间隔"], 15)
        print("更新喊话1间隔: " .. tostring(config.userConfig.喊话设置.喊话1.interval))
    end

    -- 喊话2设置（注意UI中可能有重复的"喊话1"键，这里假设是"喊话2"）

    if uiConfig["喊话2"] ~= nil then
        config.userConfig.喊话设置.喊话2.enabled = toBool(uiConfig["喊话2"])
        print("更新喊话2启用: " .. tostring(config.userConfig.喊话设置.喊话2.enabled))
    end

    if uiConfig["喊话2内容"] ~= nil then
        config.userConfig.喊话设置.喊话2.content = toString(uiConfig["喊话2内容"], "")
        print("更新喊话2内容: " .. tostring(config.userConfig.喊话设置.喊话2.content))
    end

    if uiConfig["喊话2间隔"] ~= nil then
        config.userConfig.喊话设置.喊话2.interval = toNumber(uiConfig["喊话2间隔"], 20)
        print("更新喊话2间隔: " .. tostring(config.userConfig.喊话设置.喊话2.interval))
    end

    -- 4. 更新武林争霸配置
    if uiConfig["武林争霸"] ~= nil then
        config.userConfig.武林争霸.enable = toBool(uiConfig["武林争霸"])
        print("更新武林争霸: " .. tostring(config.userConfig.武林争霸.enable))
    end

    -- 5. 更新世界Boss配置
    if uiConfig["世界Boss"] ~= nil then
        config.userConfig.世界Boss.enable = toBool(uiConfig["世界Boss"])
        print("更新世界Boss: " .. tostring(config.userConfig.世界Boss.enable))
    end

    -- 6. 更新禁地配置
    if uiConfig["禁地"] ~= nil then
        config.userConfig.禁地.enable = toBool(uiConfig["禁地"])
        print("更新禁地: " .. tostring(config.userConfig.禁地.enable))
    end

    -- 禁地层数配置
    local 禁地层数 = {}
    for i = 1, 5 do
        local key = "禁地" .. i .. "层"
        if uiConfig[key] ~= nil and toBool(uiConfig[key]) then
            table.insert(禁地层数, i .. "层")
            print("启用禁地" .. i .. "层")
        end
    end
    if #禁地层数 > 0 then
        config.userConfig.禁地.层数 = 禁地层数
        print("更新禁地层数: " .. table.concat(禁地层数, ", "))
    end

    -- 7. 更新个人首领配置
    if uiConfig["个人首领"] ~= nil then
        config.userConfig.个人首领.enable = toBool(uiConfig["个人首领"])
        print("更新个人首领: " .. tostring(config.userConfig.个人首领.enable))
    end

    -- 个人首领分配次数
    local 分配次数 = {}
    if uiConfig["个人首领_元素"] ~= nil then
        分配次数.元素 = toNumber(uiConfig["个人首领_元素"], 1)
        print("更新个人首领元素次数: " .. tostring(分配次数.元素))
    end
    if uiConfig["个人首领_装备"] ~= nil then
        分配次数.装备 = toNumber(uiConfig["个人首领_装备"], 1)
        print("更新个人首领装备次数: " .. tostring(分配次数.装备))
    end
    if uiConfig["个人首领_龙脉"] ~= nil then
        分配次数.龙脉 = toNumber(uiConfig["个人首领_龙脉"], 1)
        print("更新个人首领龙脉次数: " .. tostring(分配次数.龙脉))
    end
    if uiConfig["个人首领_神器"] ~= nil then
        分配次数.神器 = toNumber(uiConfig["个人首领_神器"], 5)
        print("更新个人首领神器次数: " .. tostring(分配次数.神器))
    end

    -- 如果有任何分配次数更新，则更新配置
    if next(分配次数) then
        for key, value in pairs(分配次数) do
            config.userConfig.个人首领.分配次数[key] = value
        end
    end

    -- 8. 更新入道天途配置
    if uiConfig["入道天途"] ~= nil then
        config.userConfig.入道天途.enable = toBool(uiConfig["入道天途"])
        print("更新入道天途: " .. tostring(config.userConfig.入道天途.enable))
    end

    -- 9. 更新升级宝箱配置（对应UI中的"领取宝箱"）
    if uiConfig["领取宝箱"] ~= nil then
        config.userConfig.升级宝箱.enable = toBool(uiConfig["领取宝箱"])
        print("更新升级宝箱: " .. tostring(config.userConfig.升级宝箱.enable))
    end

    -- 10. 更新散人福利配置
    if uiConfig["散人福利"] ~= nil then
        config.userConfig.散人福利.enable = toBool(uiConfig["散人福利"])
        print("更新散人福利: " .. tostring(config.userConfig.散人福利.enable))
    end

    -- 11. 更新星空秘境配置
    if uiConfig["星空秘境"] ~= nil then
        config.userConfig.星空秘境 = { enable = toBool(uiConfig["星空秘境"]) }
        print("更新星空秘境: " .. tostring(config.userConfig.星空秘境.enable))
    end

    -- 12. 更新千里传音配置
    if uiConfig["千里传音（本服）"] ~= nil then
        config.userConfig.千里传音.enable = toBool(uiConfig["千里传音（本服）"])
        print("更新千里传音: " .. tostring(config.userConfig.千里传音.enable))
    end

    if uiConfig["千里传音内容"] ~= nil then
        config.userConfig.千里传音.传音内容 = toString(uiConfig["千里传音内容"], "")
        print("更新千里传音内容: " .. tostring(config.userConfig.千里传音.传音内容))
    end

    print("用户配置更新完成")
end

return config
