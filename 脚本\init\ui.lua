-- ======================================================================
-- UI 配置文件
-- 将可变内容、重复内容都集中在此处，方便未来修改
-- 返回值：UI配置数据，用于系统配置合并
-- ======================================================================
local UI = {
    窗口名 = "渔夫传奇助手",
    标签ID = "标签页",
    savePath = getWorkPath() .. "winConfig.txt",
    uiConfig = "",

    -- 1. 标签页配置
    -- 格式: { {ID, 显示名}, ... }
    标签页 = {
        {"首页", "首页"},
        {"功能", "功能"},
        {"说明", "说明"},
    },
}


-- ======================================================================
-- 核心功能函数
-- ======================================================================

-- 运行按钮函数
function runFunc()
    UI.uiConfig = ui.getData()
    ui.saveProfile(UI.savePath)
    ui.dismiss(UI.窗口名)
    breakPoint = false
end

function downloadUIConfig()
    -- 1. 从UI读取配置名
    local configName = ui.getValue("界面配置名")
    if configName == "" then
        toast("请输入配置名",0,0,16)
        return false
    end
    print(configName)

    -- 2. 保存当前的card_key值
    local originalCardKey = ui.getValue("card_key")
    print("2. 保存原始card_key值: " .. tostring(originalCardKey))

    -- 3. 从服务器下载配置
    local configContent, message = client:downloadUIConfig(configName)
    if configContent then
        -- 4. 保存配置到本地
        local file = io.open(UI.savePath, "w")
        if file then
            file:write(configContent)
            file:close()
            print("4. 保存配置成功")
        end
    else
        toast("下载失败: " .. message, 0, 0, 16)
        return false
    end

    print(configContent)
    -- 5. 加载配置
    ui.loadProfile(UI.savePath)

    -- 6. 恢复原来的card_key值
    if originalCardKey and originalCardKey ~= "" then
        ui.setText("card_key", originalCardKey)
        print("6. 恢复card_key值: " .. tostring(originalCardKey))
    end

    toast("加载配置成功",0,0,16)

end

function uploadUIConfig()
    -- 1. 从UI读取配置名
    local configName = ui.getValue("界面配置名")
    if configName == "" then
        toast("请输入配置名",0,0,16)
        return false
    end
    print(configName)


    --2. 保存当前配置到本地
    ui.saveProfile(UI.savePath)

    -- 3. 从本地读取配置
    local file = io.open(UI.savePath, "r")
    local configContent
    if file then
        configContent = file:read("*all")
        file:close()
        print("3. 从文件读取的内容:")
        print(configContent)
    else
        print("3. 读取文件失败: " .. UI.savePath)
        return
    end

    -- 4. 将JSON转换为Lua表，修改card_key，再转回JSON
    local configTable = jsonLib.decode(configContent)
    if configTable and configTable.card_key then
        print("4. 原始card_key值: " .. tostring(configTable.card_key))
        configTable.card_key = ""
        print("4. 修改后card_key值: " .. tostring(configTable.card_key))
        configContent = jsonLib.encode(configTable)
        print("4. 修改后的JSON内容:")
        print(configContent)
    else
        print("4. 配置中未找到card_key字段")
    end

    -- 5. 上传配置到服务器
    local success, message = client:uploadUIConfig(configName, configContent)
    if success then
        toast("上传成功: " .. message, 0, 0, 16)
    else
        toast("上传失败: " .. message, 0, 0, 16)
    end

end
-- ======================================================================
-- UI 辅助创建函数 (Helper Functions)
-- ======================================================================

-- 创建新行
local function createRow(win, rowid, w, h)
    -- 如果没有传入rowid，则使用UUID风格生成
    if not rowid then
        local chars = "0123456789abcdef"
        local uuid = ""
        -- 修正了循环，确保每次都生成一个字符，总共8个
        for i = 1, 8 do
            local rand_char_pos = math.random(#chars) -- #chars 等于 16
            uuid = uuid .. chars:sub(rand_char_pos, rand_char_pos)
        end
        rowid = "row_" .. uuid .. "_" .. os.time()
    end
    ui.newRow(win, rowid, w or -2, h or -2)
end

-- 创建带分割线的标题
local function createHeadline(name, title)
    local headlineId = "headline_" .. title:gsub("%s+", "") -- 移除空格以创建有效ID
    local lineId = headlineId .. "_line"
    local rowId = headlineId .. "_row"
    local creativeTitle =  "🎣"..title
    createRow(name, rowId, -1, -2)
    ui.addTextView(name, headlineId, creativeTitle)
    ui.addLine(name, lineId, -1, 6)

    ui.setTextColor(headlineId, "#FF2196F3")
    ui.setBackground(lineId, "#FF2196F3")

    createRow(name, rowId .. "2", -1, -2)
end

-- 快速创建彩色文本视图
-- 参数：所属标签名, 控件ID, 显示文字, 背景色(可选，默认#ff36454f), 文字颜色(可选，默认#ffffa500)
local function createColoredTextView(tabName, textId, text, backgroundColor, textColor)
    ui.addTextView(tabName, textId, text)
    ui.setBackground(textId, backgroundColor or "#ff36454f")
    ui.setTextColor(textId, textColor or "#ffffa500")
end

-- 快速创建带提示文本的编辑框
-- 参数：所属标签名, 控件ID, 提示文本
local function createEditTextWithHint(tabName, editId, hintText)
    ui.addEditText(tabName, editId, "")
    if hintText then
        ui.setEditHintText(editId, hintText)
    end
end

-- ======================================================================
-- 主窗口与UI布局
-- ======================================================================

-- 1. 初始化主窗口和标签页
local function windows()
    ui.newLayout(UI.窗口名, 700, 700)
    ui.addTabView(UI.窗口名, UI.标签ID)

    -- 根据配置自动创建标签页
    for _, tab in ipairs(UI.标签页) do
        ui.addTab(UI.标签ID, tab[1], tab[2])
    end

    -- 主窗口底部的按钮
    ui.newRow(UI.窗口名, "winrow", -2, -2)
    ui.addButton(UI.窗口名, "winClose", "退出", 345, 80)
    ui.addButton(UI.窗口名, "runClose", "运行", 345, 80)
    ui.setBackground(UI.窗口名, "#F2FAF7F0")

    --设置按钮事件
    ui.setOnClick("winClose", "exitScript()")
    ui.setOnClick("runClose", "runFunc()")

    -- 设置按钮颜色
    ui.setBackground("winClose", "#FF6495ED") -- 退出按钮 (矢车菊蓝)
    ui.setTextColor("winClose", "#FFFFFFFF")
    ui.setBackground("runClose", "#FF4285F4") -- 运行按钮 (谷歌蓝)
    ui.setTextColor("runClose", "#FFFFFFFF")
end

-- 开始创建窗口
windows()



-- 2. 为每个标签页填充内容

-- ======================= 首页 =======================
createHeadline("首页", "介绍")
ui.addTextView("首页", "作者介绍", "欢迎使用辅助 \n作者：渔夫")

createHeadline("首页", "卡密")
ui.addTextView("首页", "卡密介绍", "请输入卡密：")
ui.addEditText("首页", "card_key", "")

createHeadline("首页", "网络配置")
createColoredTextView("首页", "网络配置提示", "说明：把配置上传到服务器，方便多开用户使用")


createRow("首页")
ui.addTextView("首页", "网络介绍", "请输入配置名：")
ui.addEditText("首页", "界面配置名", "")

createRow("首页")
ui.addButton("首页","上传配置","上传配置",175,60)
ui.addButton("首页","下载配置","下载配置",175,60)
ui.setBackground("上传配置", "#ff343a40")
ui.setTextColor("上传配置", "#ffffffff")
ui.setOnClick("上传配置","uploadUIConfig()")
ui.setBackground("下载配置", "#ff17a2b8")
ui.setTextColor("下载配置", "#ffffffff")
ui.setOnClick("下载配置","downloadUIConfig()")



-- ======================= 功能 =======================
createHeadline("功能", "地图选择")
ui.addCheckBox("功能", "进入地图", "进入地图", false)
ui.addSpinner("功能", "下图模式", {"无尽幽白一层", "离火圣殿二层","时空幽谷","遗忘之地"}, 0)

createHeadline("功能", "定时重启")
ui.addCheckBox("功能", "定时重启", "定时重启", false)
createEditTextWithHint("功能", "定时重启时间", "输入重启间隔（分钟）")

createHeadline("功能","每日功能")
ui.addCheckBox("功能", "散人福利", "散人福利", false)
ui.addCheckBox("功能", "世界Boss", "世界Boss", false)
ui.addCheckBox("功能", "入道天途", "入道天途", false)
createRow("功能")
ui.addCheckBox("功能", "武林争霸", "武林争霸", false)
ui.addCheckBox("功能", "星空秘境", "星空秘境", false)
ui.addCheckBox("功能", "领取宝箱", "领取宝箱", false)



createHeadline("功能", "个人首领")
ui.addCheckBox("功能", "个人首领", "个人首领", false)
createColoredTextView("功能", "个人首领提示", "下面输入框为每个首领分配次数")
createRow("功能")
createEditTextWithHint("功能","个人首领_元素","元素")
createEditTextWithHint("功能","个人首领_装备","装备")
createEditTextWithHint("功能","个人首领_龙脉","龙脉")
createEditTextWithHint("功能","个人首领_神器","神器")

createHeadline("功能", "禁地")
ui.addCheckBox("功能","禁地","禁地")
createColoredTextView("功能", "禁地扫荡提示", "下面选择需要扫荡的禁地层数")
createRow("功能")
ui.addCheckBox("功能","禁地1层","1层")
ui.addCheckBox("功能","禁地2层","2层")
ui.addCheckBox("功能","禁地3层","3层")
ui.addCheckBox("功能","禁地4层","4层")
ui.addCheckBox("功能","禁地5层","5层")

createHeadline("功能", "千里传音")
ui.addCheckBox("功能", "千里传音（本服）", "千里传音", false)
createEditTextWithHint("功能", "千里传音内容", "输入传音内容")

createHeadline("功能", "喊话设置")
ui.addCheckBox("功能", "喊话1", "喊话1", false)
createEditTextWithHint("功能", "喊话1间隔", "输入喊话间隔（秒）")
createRow("功能")
createEditTextWithHint("功能", "喊话1内容", "输入喊话内容")

createRow("功能")
ui.addCheckBox("功能", "喊话2", "喊话2", false)
createEditTextWithHint("功能", "喊话2间隔", "输入喊话间隔（秒）")
createRow("功能")
createEditTextWithHint("功能", "喊话2内容", "输入喊话内容")



-- ======================= 说明 =======================

-- 批量创建带编号的说明文本函数
local function createNumberedInstructions(tabName, instructionList)
    -- 定义颜色配对，使用深色文字配浅色背景，适合白色主题
    local colorPairs = {
        {bg = "#ffe2e8f0", text = "#ff1e293b"},  -- 浅灰背景+深灰文字
        {bg = "#ffdbeafe", text = "#ff1e40af"},  -- 浅蓝背景+深蓝文字  
        {bg = "#ffdcfce7", text = "#ff166534"},  -- 浅绿背景+深绿文字
        {bg = "#fffed7aa", text = "#ff9a3412"},  -- 浅橙背景+深橙文字
        {bg = "#ffe9d5ff", text = "#ff7c3aed"},  -- 浅紫背景+深紫文字
        {bg = "#ffa7f3d0", text = "#ff047857"}   -- 浅青背景+深青文字
    }
    
    for i, text in ipairs(instructionList) do
        local textId = "脚本运行说明" .. i
        local colorIndex = ((i - 1) % #colorPairs) + 1
        local colors = colorPairs[colorIndex]
        createColoredTextView(tabName, textId, i .. ". " .. text, colors.bg, colors.text)
        createRow(tabName)
    end
end

createHeadline("说明", "脚本运行说明")
-- 使用新函数创建说明文本
local 说明文本 = {
    "武林争霸：每天13点和21点执行",
    "散人福利和领取宝箱：每天22点执行",
    "世界BOSS：挂机时每隔15分钟检测一次，超过两次没检测到今日不再检测",
    "禁地扫荡：脚本启动时扫荡一次，往后每隔6小时进行一次",
    "星空秘境：每天20:02自动执行",
    "千里传音：每天19:40、19:58、22:29执行",
    "个人首领和入道天途：每天执行一次"
}
createNumberedInstructions("说明", 说明文本)


-- ======================================================================
-- 脚本启动与循环
-- ======================================================================

-- 显示UI
breakPoint = true
ui.loadProfile(UI.savePath)
ui.show(UI.窗口名, false)

while breakPoint do
    sleep(100)
end

-- 返回UI配置
return UI.uiConfig
